@echo off
title GEMINI CLI UI - INTERFACE
color 0A

echo ========================================
echo   GEMINI CLI UI - DEMARRAGE
echo ========================================
echo.

echo Verification rapide...
if not exist "package.json" (
    echo ERREUR: Mauvais repertoire !
    echo Vous devez etre dans le dossier ui/
    echo.
    pause
    exit /b 1
)

echo Repertoire: %CD%
echo.
echo IMPORTANT:
echo - Cette fenetre doit rester OUVERTE
echo - L'interface sera sur http://localhost:4009
echo - Appuyez sur Ctrl+C pour arreter
echo.
echo Demarrage en cours...
echo.

REM Utiliser le script dev qui lance serveur + client
npm run dev

echo.
echo ========================================
echo L'interface s'est arretee
echo ========================================
echo.
echo Appuyez sur une touche pour fermer...
pause >nul
