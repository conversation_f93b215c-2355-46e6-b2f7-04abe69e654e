# 🎯 Guide Complet d'Utilisation de Gemini

## 🤔 Quelle Méthode Choisir ?

Vous avez maintenant **3 options principales** pour utiliser Gemini. Voici un guide pour vous aider à choisir :

## 🚀 Option 1 : NPX en Ligne de Commande (RECOMMANDÉE pour vous)

### ✅ Pourquoi cette option est parfaite pour vous :
- **Simple et directe** - Une seule commande
- **Toujours à jour** - Version la plus récente automatiquement
- **Gemini 2.5 Pro** - Le modèle que vous préférez
- **Pas d'installation** - Fonctionne immédiatement

### 📝 Commandes à utiliser :

#### Commande de base :
```powershell
npx https://github.com/google-gemini/gemini-cli --model gemini-2.5-pro
```

#### Avec votre clé API :
```powershell
$env:GEMINI_API_KEY="YOUR_GEMINI_API_KEY_HERE"
npx https://github.com/google-gemini/gemini-cli --model gemini-2.5-pro
```

#### Question directe :
```powershell
$env:GEMINI_API_KEY="YOUR_GEMINI_API_KEY_HERE"
echo "Votre question" | npx https://github.com/google-gemini/gemini-cli --model gemini-2.5-pro
```

#### Avec notre script simplifié :
```powershell
.\start-gemini-npx.ps1
```

## 🏠 Option 2 : Installation Locale (Configuration Française)

### ✅ Avantages :
- **Configuration française permanente** - Prompt système traduit
- **Plus rapide** - Pas de téléchargement à chaque fois
- **Contrôle total** - Personnalisation avancée

### 📝 Commandes à utiliser :
```powershell
.\start-gemini.ps1                    # Script principal
node bundle/gemini.js --model gemini-2.5-pro  # Commande directe
```

## 💻 Option 3 : Visual Studio Code avec Gemini Code Assist

### 🔍 C'est quoi Gemini Code Assist ?
Gemini Code Assist est une **extension différente** pour Visual Studio Code qui offre :
- **Autocomplétion de code** intelligente
- **Suggestions en temps réel** pendant que vous codez
- **Intégration directe** dans l'éditeur
- **Assistance contextuelle** basée sur votre code

### ⚖️ Comparaison : CLI vs Code Assist

| Fonctionnalité | Gemini CLI (npx) | Gemini Code Assist |
|----------------|------------------|-------------------|
| **Usage** | Ligne de commande | Intégré dans VS Code |
| **Type d'aide** | Questions/réponses générales | Assistance au codage |
| **Contexte** | Projet entier | Fichier en cours |
| **Autocomplétion** | ❌ Non | ✅ Oui |
| **Chat général** | ✅ Oui | ✅ Limité |
| **Outils système** | ✅ Oui (fichiers, shell) | ❌ Non |

## 🎯 Ma Recommandation pour Vous

### 🥇 **Continuez avec NPX** (Votre méthode actuelle)

**Pourquoi ?**
1. ✅ **Vous maîtrisez déjà** cette méthode
2. ✅ **Fonctionne parfaitement** avec Gemini 2.5 Pro
3. ✅ **Polyvalent** - Questions générales + aide au code
4. ✅ **Outils puissants** - Lecture/écriture de fichiers, shell, etc.
5. ✅ **Pas de configuration supplémentaire** nécessaire

### 🥈 **Ajoutez Code Assist** (En complément)

**Quand l'utiliser ?**
- ✅ **Pendant le codage** - Autocomplétion intelligente
- ✅ **Suggestions rapides** - Corrections de code
- ✅ **Refactoring** - Amélioration du code existant

## 📋 Guide Pratique d'Utilisation

### 🔄 Workflow Recommandé :

#### 1. **Questions Générales** → NPX
```powershell
# Exemple : Expliquer un concept
echo "Expliquez-moi les promesses en JavaScript" | npx https://github.com/google-gemini/gemini-cli --model gemini-2.5-pro
```

#### 2. **Analyse de Projet** → NPX
```powershell
# Exemple : Analyser un fichier
npx https://github.com/google-gemini/gemini-cli --model gemini-2.5-pro
# Puis dans le chat : "Analysez le fichier src/main.js"
```

#### 3. **Codage en Temps Réel** → Code Assist
- Ouvrez VS Code
- Installez l'extension Gemini Code Assist
- Codez normalement, les suggestions apparaissent automatiquement

### 🛠️ Installation de Code Assist (Optionnel)

Si vous voulez essayer Code Assist en complément :

1. **Ouvrir VS Code**
2. **Extensions** (Ctrl+Shift+X)
3. **Rechercher** "Gemini Code Assist"
4. **Installer** l'extension officielle Google
5. **Configurer** avec votre clé API

## 🎯 Résumé des Commandes Principales

### 🚀 NPX (Votre méthode principale) :
```powershell
# Configuration de la clé API
$env:GEMINI_API_KEY="YOUR_GEMINI_API_KEY_HERE"

# Lancement interactif
npx https://github.com/google-gemini/gemini-cli --model gemini-2.5-pro

# Question directe
echo "Votre question" | npx https://github.com/google-gemini/gemini-cli --model gemini-2.5-pro

# Avec notre script
.\start-gemini-npx.ps1
```

### 🏠 Installation Locale (Alternative) :
```powershell
.\start-gemini.ps1
```

## 🏆 Conclusion

**Continuez avec NPX !** C'est la méthode qui vous convient le mieux. Vous pouvez optionnellement ajouter Code Assist pour l'autocomplétion pendant le codage, mais NPX reste votre outil principal pour les questions générales et l'analyse de projets.
