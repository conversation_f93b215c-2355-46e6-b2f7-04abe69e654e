# Script de démarrage Gemini CLI avec NPX (version en ligne)
# Utilise la commande npx pour lancer la dernière version directement depuis GitHub

$env:GEMINI_API_KEY = "YOUR_GEMINI_API_KEY_HERE"
$env:GEMINI_MODEL = "gemini-2.5-pro"
$env:GEMINI_SYSTEM_MD = "true"

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "  GEMINI CLI NPX - VERSION FRANÇAISE" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "Configuration:" -ForegroundColor Green
Write-Host "- Modèle: $($env:GEMINI_MODEL)" -ForegroundColor Yellow
Write-Host "- Prompt système français: Activé" -ForegroundColor Yellow
Write-Host "- Clé API: Configurée" -ForegroundColor Yellow
Write-Host "- Source: GitHub (npx)" -ForegroundColor Yellow
Write-Host ""
Write-Host "Commande utilisée:" -ForegroundColor Green
Write-Host "npx https://github.com/google-gemini/gemini-cli --model gemini-2.5-pro" -ForegroundColor White
Write-Host ""

# Démarrage de Gemini CLI via NPX
npx https://github.com/google-gemini/gemini-cli --model gemini-2.5-pro $args
