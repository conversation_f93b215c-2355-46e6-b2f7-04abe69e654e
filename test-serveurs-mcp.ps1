# Script de test des serveurs MCP gratuits
# Teste la disponibilité et le fonctionnement des serveurs MCP configurés

Write-Host "🧪 TEST DES SERVEURS MCP GRATUITS" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan
Write-Host ""

# Configuration de la clé API (remplacez par votre vraie clé)
$env:GEMINI_API_KEY = "YOUR_GEMINI_API_KEY_HERE"
$env:GEMINI_MODEL = "gemini-2.5-pro"
$env:GEMINI_SYSTEM_MD = "true"

# Liste des serveurs MCP à tester
$serveurs_mcp = @{
    "Context7" = "@context7/mcp-server"
    "Filesystem" = "@modelcontextprotocol/server-filesystem"
    "Git" = "@modelcontextprotocol/server-git"
    "Weather" = "weather-mcp"
    "Calculator" = "mcp-server-calculator"
    "Fetch" = "@modelcontextprotocol/server-fetch"
    "DateTime" = "mcp-datetime"
}

Write-Host "📋 Serveurs MCP configurés :" -ForegroundColor Yellow
foreach ($serveur in $serveurs_mcp.GetEnumerator()) {
    Write-Host "   - $($serveur.Key): $($serveur.Value)" -ForegroundColor Gray
}
Write-Host ""

# Test de disponibilité des packages
Write-Host "🔍 Test de disponibilité des packages NPX..." -ForegroundColor Yellow
$packages_disponibles = @()
$packages_manquants = @()

foreach ($serveur in $serveurs_mcp.GetEnumerator()) {
    Write-Host "   Vérification de $($serveur.Key)..." -ForegroundColor Gray
    
    try {
        $result = npx -y $serveur.Value --help 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "   ✅ $($serveur.Key) - Disponible" -ForegroundColor Green
            $packages_disponibles += $serveur.Key
        } else {
            Write-Host "   ❌ $($serveur.Key) - Non disponible" -ForegroundColor Red
            $packages_manquants += $serveur.Key
        }
    } catch {
        Write-Host "   ❌ $($serveur.Key) - Erreur de test" -ForegroundColor Red
        $packages_manquants += $serveur.Key
    }
}

Write-Host ""

# Résumé des tests
Write-Host "📊 RÉSUMÉ DES TESTS" -ForegroundColor Yellow
Write-Host "==================" -ForegroundColor Yellow
Write-Host ""

Write-Host "✅ Packages disponibles ($($packages_disponibles.Count)) :" -ForegroundColor Green
foreach ($package in $packages_disponibles) {
    Write-Host "   - $package" -ForegroundColor Green
}

if ($packages_manquants.Count -gt 0) {
    Write-Host ""
    Write-Host "❌ Packages manquants ($($packages_manquants.Count)) :" -ForegroundColor Red
    foreach ($package in $packages_manquants) {
        Write-Host "   - $package" -ForegroundColor Red
    }
}

Write-Host ""

# Test de configuration Gemini CLI
Write-Host "🔧 Test de configuration Gemini CLI..." -ForegroundColor Yellow

if (Test-Path ".gemini/settings.json") {
    Write-Host "✅ Fichier settings.json trouvé" -ForegroundColor Green
    
    try {
        $settings = Get-Content ".gemini/settings.json" | ConvertFrom-Json
        
        if ($settings.mcpServers) {
            $nb_serveurs = ($settings.mcpServers | Get-Member -MemberType NoteProperty).Count
            Write-Host "✅ $nb_serveurs serveurs MCP configurés" -ForegroundColor Green
            
            Write-Host "   Serveurs configurés :" -ForegroundColor Gray
            foreach ($serveur in $settings.mcpServers.PSObject.Properties) {
                Write-Host "   - $($serveur.Name): $($serveur.Value.description)" -ForegroundColor Gray
            }
        } else {
            Write-Host "❌ Aucun serveur MCP configuré" -ForegroundColor Red
        }
        
        if ($settings.auth.api_key -eq "YOUR_GEMINI_API_KEY_HERE") {
            Write-Host "⚠️  Clé API non configurée (placeholder détecté)" -ForegroundColor Yellow
        } else {
            Write-Host "✅ Clé API configurée" -ForegroundColor Green
        }
        
    } catch {
        Write-Host "❌ Erreur de lecture du fichier settings.json" -ForegroundColor Red
    }
} else {
    Write-Host "❌ Fichier settings.json non trouvé" -ForegroundColor Red
}

Write-Host ""

# Instructions pour la suite
Write-Host "🎯 PROCHAINES ÉTAPES" -ForegroundColor Yellow
Write-Host "====================" -ForegroundColor Yellow
Write-Host ""

if ($packages_disponibles.Count -gt 0) {
    Write-Host "✅ Serveurs MCP prêts à utiliser !" -ForegroundColor Green
    Write-Host ""
    Write-Host "Pour tester avec Gemini CLI :" -ForegroundColor White
    Write-Host "1. Configurez votre clé API dans .gemini/settings.json" -ForegroundColor White
    Write-Host "2. Lancez : .\start-gemini.ps1" -ForegroundColor White
    Write-Host "3. Testez : 'Quels outils MCP sont disponibles ?'" -ForegroundColor White
    Write-Host "4. Utilisez : 'Lis le fichier package.json'" -ForegroundColor White
} else {
    Write-Host "❌ Aucun serveur MCP disponible" -ForegroundColor Red
    Write-Host "Vérifiez votre connexion internet et réessayez" -ForegroundColor White
}

if ($packages_manquants.Count -gt 0) {
    Write-Host ""
    Write-Host "⚠️  Packages manquants détectés" -ForegroundColor Yellow
    Write-Host "Ces packages seront téléchargés automatiquement lors de la première utilisation" -ForegroundColor White
}

Write-Host ""
Write-Host "📚 Documentation complète : SERVEURS-MCP-GRATUITS.md" -ForegroundColor Cyan
Write-Host "🔧 Configuration étendue : .gemini/settings-mcp-extended.json.example" -ForegroundColor Cyan
Write-Host ""
Write-Host "🎉 Test terminé !" -ForegroundColor Green
