<svg width="192" height="192" viewBox="0 0 192 192" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="geminiGrad192" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#06b6d4;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#0891b2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0e7490;stop-opacity:1" />
    </linearGradient>
    <filter id="glow192">
      <feGaussianBlur stdDeviation="9.600000000000001" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background with gradient -->
  <rect x="0" y="0" width="192" height="192" rx="36" fill="url(#geminiGrad192)"/>
  
  <!-- Gemini constellation symbol -->
  <g transform="translate(96,96)" filter="url(#glow192)">
    <!-- Top star shape -->
    <path d="M0,-60 L15,0 L-15,0 Z" fill="white" opacity="0.95"/>
    <!-- Bottom star shape -->
    <path d="M0,60 L-15,0 L15,0 Z" fill="white" opacity="0.85"/>
    <!-- Connecting elements -->
    <circle cx="0" cy="-30" r="9" fill="white" opacity="0.9"/>
    <circle cx="0" cy="30" r="9" fill="white" opacity="0.9"/>
    <line x1="0" y1="-30" x2="0" y2="30" stroke="white" stroke-width="6" opacity="0.7"/>
  </g>
</svg>