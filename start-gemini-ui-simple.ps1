# Script de lancement Gemini CLI UI - Version Simple (Sans Terminal Intégré)
# Auteur: Cisco-FlexoDiv
# Version: 1.0

Write-Host "🚀 Démarrage de Gemini CLI UI - Version Simple" -ForegroundColor Green
Write-Host "=============================================" -ForegroundColor Cyan

# Vérification des prérequis
Write-Host "🔍 Vérification des prérequis..." -ForegroundColor Yellow

# Vérifier Node.js
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js détecté: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js n'est pas installé ou non accessible" -ForegroundColor Red
    Write-Host "   Veuillez installer Node.js v20 ou supérieur" -ForegroundColor Red
    exit 1
}

# Naviguer vers le dossier UI
Write-Host "📁 Navigation vers le dossier UI..." -ForegroundColor Yellow
Set-Location ui

# Vérifier l'installation des dépendances
if (-not (Test-Path "node_modules")) {
    Write-Host "📦 Installation des dépendances..." -ForegroundColor Yellow
    npm install --ignore-scripts
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Erreur lors de l'installation des dépendances" -ForegroundColor Red
        exit 1
    }
}

# Vérifier le fichier .env
if (-not (Test-Path ".env")) {
    Write-Host "⚠️  Fichier .env manquant, copie depuis .env.example..." -ForegroundColor Yellow
    Copy-Item ".env.example" ".env"
}

Write-Host "🌐 Démarrage du serveur frontend..." -ForegroundColor Green
Write-Host "   - Interface UI: http://localhost:4009" -ForegroundColor Cyan
Write-Host "" -ForegroundColor White
Write-Host "📝 Instructions:" -ForegroundColor Yellow
Write-Host "   1. L'interface s'ouvrira automatiquement dans votre navigateur" -ForegroundColor White
Write-Host "   2. Cette version fonctionne sans terminal intégré" -ForegroundColor White
Write-Host "   3. Utilisez votre terminal habituel pour Gemini CLI" -ForegroundColor White
Write-Host "   4. Utilisez Ctrl+C pour arrêter le serveur" -ForegroundColor White
Write-Host "" -ForegroundColor White

# Démarrer seulement le frontend
npm run client
