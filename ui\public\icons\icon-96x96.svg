<svg width="96" height="96" viewBox="0 0 96 96" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="geminiGrad96" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#06b6d4;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#0891b2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0e7490;stop-opacity:1" />
    </linearGradient>
    <filter id="glow96">
      <feGaussianBlur stdDeviation="4.800000000000001" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background with gradient -->
  <rect x="0" y="0" width="96" height="96" rx="18" fill="url(#geminiGrad96)"/>
  
  <!-- Gemini constellation symbol -->
  <g transform="translate(48,48)" filter="url(#glow96)">
    <!-- Top star shape -->
    <path d="M0,-30 L7.5,0 L-7.5,0 Z" fill="white" opacity="0.95"/>
    <!-- Bottom star shape -->
    <path d="M0,30 L-7.5,0 L7.5,0 Z" fill="white" opacity="0.85"/>
    <!-- Connecting elements -->
    <circle cx="0" cy="-15" r="4.5" fill="white" opacity="0.9"/>
    <circle cx="0" cy="15" r="4.5" fill="white" opacity="0.9"/>
    <line x1="0" y1="-15" x2="0" y2="15" stroke="white" stroke-width="3" opacity="0.7"/>
  </g>
</svg>