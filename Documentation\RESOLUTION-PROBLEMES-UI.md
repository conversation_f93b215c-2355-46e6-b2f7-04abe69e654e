# 🔧 Résolution des Problèmes - Gemini CLI UI

## 🚨 Problème Principal : node-pty sur Windows

### Symptôme
```
error MSB8040: des bibliothèques avec atténuations de Spectre sont nécessaires pour ce projet
```

### Cause
Le package `node-pty` nécessite des outils de compilation C++ spécifiques sur Windows qui ne sont pas toujours disponibles.

## 🛠️ Solutions Proposées

### Solution 1 : Installation des Outils Visual Studio (Recommandée)
```powershell
# Installer les outils de build Windows
npm install -g windows-build-tools

# Ou installer Visual Studio Build Tools avec les composants Spectre
# Télécharger depuis : https://visualstudio.microsoft.com/downloads/#build-tools-for-visual-studio-2022
```

### Solution 2 : Utilisation sans Terminal Intégré
```powershell
# Utiliser le script simplifié
.\start-gemini-ui-simple.ps1
```

### Solution 3 : Installation Alternative de node-pty
```powershell
cd ui
npm uninstall node-pty
npm install node-pty-prebuilt-multiarch
```

### Solution 4 : Utilisation avec Docker (Avancée)
```dockerfile
# Créer un Dockerfile dans le dossier ui/
FROM node:20-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
EXPOSE 4008 4009
CMD ["npm", "run", "dev"]
```

## 🎯 Fonctionnalités Disponibles Sans node-pty

### ✅ Fonctionnalités Opérationnelles
- Interface web responsive
- Chat avec Gemini CLI (via API)
- Explorateur de fichiers
- Éditeur de code avec coloration syntaxique
- Gestion des sessions
- Authentification utilisateur
- Intégration Git (lecture)

### ❌ Fonctionnalités Limitées
- Terminal intégré (nécessite node-pty)
- Exécution de commandes shell directes
- Certaines opérations Git avancées

## 🔄 Alternatives de Contournement

### Pour le Terminal
- Utilisez votre terminal Windows habituel
- Ouvrez PowerShell ou CMD dans le dossier du projet
- Exécutez `npx @google/gemini-cli` directement

### Pour les Commandes Git
- Utilisez Git Bash ou votre client Git préféré
- L'interface affiche les changements mais les commits se font en externe

## 📋 Étapes de Dépannage

### 1. Vérification de l'Environnement
```powershell
# Vérifier Node.js
node --version

# Vérifier npm
npm --version

# Vérifier Python (requis pour node-gyp)
python --version

# Vérifier Visual Studio Build Tools
where msbuild
```

### 2. Nettoyage et Réinstallation
```powershell
cd ui
Remove-Item -Recurse -Force node_modules
Remove-Item package-lock.json
npm cache clean --force
npm install --ignore-scripts
```

### 3. Test de Fonctionnement
```powershell
# Test du serveur backend seul
node server/index.js

# Test du client seul
npm run client
```

## 🎯 Recommandations

### Pour une Utilisation Immédiate
1. Utilisez `.\start-gemini-ui-simple.ps1`
2. Acceptez les limitations du terminal intégré
3. Utilisez votre terminal habituel en parallèle

### Pour une Installation Complète
1. Installez Visual Studio Build Tools avec les composants Spectre
2. Réinstallez les dépendances avec `npm install`
3. Utilisez `.\start-gemini-ui.ps1`

## 📞 Support Technique

### Logs Utiles
- Logs npm : `C:\Users\<USER>\AppData\Local\npm-cache\_logs\`
- Logs node-gyp : Affichés dans la console lors de l'installation
- Logs serveur : Console où le serveur est lancé

### Commandes de Diagnostic
```powershell
# Informations système
npm config list
node -p "process.platform + ' ' + process.arch"

# Test de compilation
npm install node-gyp -g
node-gyp configure
```

---

**📝 Note** : Ces problèmes sont courants sur Windows et ne reflètent pas un problème avec votre configuration. L'interface reste pleinement fonctionnelle sans le terminal intégré.
