# 💻 Guide d'Installation Gemini Code Assist pour VS Code

## 🎯 Qu'est-ce que Gemini Code Assist ?

Gemini Code Assist est une extension VS Code qui offre :
- ✅ **Autocomplétion intelligente** pendant que vous tapez
- ✅ **Suggestions de code** contextuelles
- ✅ **Correction d'erreurs** automatique
- ✅ **Refactoring assisté** par IA
- ✅ **Documentation automatique** du code

## 🚀 Installation Étape par Étape

### 1. **Ouvrir Visual Studio Code**
```
Lancez VS Code sur votre machine
```

### 2. **Accéder aux Extensions**
```
Méthode 1 : Ctrl + Shift + X
Méthode 2 : Cliquer sur l'icône Extensions dans la barre latérale
Méthode 3 : Menu View → Extensions
```

### 3. **Rechercher l'Extension**
```
Dans la barre de recherche, tapez :
"Gemini Code Assist" ou "Google Cloud Code"
```

### 4. **Installer l'Extension Officielle**
```
Cherchez l'extension publiée par "Google"
Cliquez sur "Install"
```

### 5. **Redémarrer VS Code**
```
Redémarrez VS Code pour activer l'extension
```

## ⚙️ Configuration avec Votre Clé API

### 1. **Ouvrir les Paramètres**
```
Ctrl + , (virgule)
Ou File → Preferences → Settings
```

### 2. **Rechercher Gemini**
```
Dans la barre de recherche des paramètres :
Tapez "gemini" ou "google cloud"
```

### 3. **Configurer la Clé API**
```
Trouvez le champ "API Key" ou "Authentication"
Entrez votre clé : YOUR_GEMINI_API_KEY_HERE
```

### 4. **Sélectionner le Modèle**
```
Dans les paramètres de l'extension :
Modèle : gemini-2.5-pro
Région : global (ou votre région préférée)
```

## 🧪 Test de Fonctionnement

### 1. **Créer un Nouveau Fichier**
```javascript
// Créez un fichier test.js
// Commencez à taper :

function calculer
```

### 2. **Vérifier l'Autocomplétion**
```javascript
// Vous devriez voir des suggestions apparaître automatiquement
function calculerSomme(a, b) {
    return a + b;
}
```

### 3. **Tester les Suggestions**
```javascript
// Tapez un commentaire et voyez si Gemini génère le code
// Calculer la factorielle d'un nombre
```

## 🔧 Configuration Avancée

### Paramètres Recommandés :
```json
{
    "gemini.model": "gemini-2.5-pro",
    "gemini.autoComplete": true,
    "gemini.suggestions": true,
    "gemini.language": "fr",
    "gemini.maxTokens": 1000
}
```

### Raccourcis Clavier Utiles :
```
Ctrl + Space : Forcer l'autocomplétion
Ctrl + Shift + P : Palette de commandes Gemini
Alt + Enter : Accepter la suggestion
Escape : Rejeter la suggestion
```

## 🎯 Utilisation Pratique

### 1. **Autocomplétion de Code**
```javascript
// Tapez le début d'une fonction
function tri
// Gemini suggérera automatiquement des implémentations
```

### 2. **Génération de Documentation**
```javascript
/**
 * Tapez /** au-dessus d'une fonction
 * Gemini générera automatiquement la JSDoc
 */
function maFonction(param1, param2) {
    // ...
}
```

### 3. **Correction d'Erreurs**
```javascript
// Gemini détectera et suggérera des corrections
// pour les erreurs de syntaxe et de logique
```

### 4. **Refactoring**
```javascript
// Sélectionnez du code et utilisez :
// Ctrl + Shift + P → "Gemini: Refactor Code"
```

## 🔄 Workflow Combiné : NPX + Code Assist

### 🎯 Stratégie Recommandée :

#### **Pour les Questions Générales** → NPX
```powershell
echo "Comment optimiser une base de données ?" | npx https://github.com/google-gemini/gemini-cli --model gemini-2.5-pro
```

#### **Pour le Codage** → Code Assist
```
Ouvrez VS Code
Commencez à coder
Laissez Code Assist vous assister automatiquement
```

#### **Pour l'Analyse de Projet** → NPX
```powershell
npx https://github.com/google-gemini/gemini-cli --model gemini-2.5-pro
# Dans le chat : "Analysez la structure de mon projet React"
```

## 🛠️ Dépannage

### Problème : Extension non trouvée
```
Solution : Vérifiez que vous cherchez "Google Cloud Code" 
ou "Gemini Code Assist" dans le marketplace officiel
```

### Problème : Clé API non reconnue
```
Solution : 
1. Vérifiez que la clé est correcte
2. Redémarrez VS Code
3. Vérifiez les paramètres de l'extension
```

### Problème : Pas de suggestions
```
Solution :
1. Vérifiez que l'extension est activée
2. Vérifiez la connexion internet
3. Redémarrez VS Code
```

## 🏆 Résumé

**Code Assist est un excellent complément à NPX :**
- ✅ **NPX** pour les questions générales et l'analyse
- ✅ **Code Assist** pour l'autocomplétion pendant le codage
- ✅ **Les deux utilisent** votre clé API et Gemini 2.5 Pro
- ✅ **Workflow optimal** : Combiner les deux selon le contexte

**Installation optionnelle** - Vous pouvez continuer uniquement avec NPX si vous préférez !
