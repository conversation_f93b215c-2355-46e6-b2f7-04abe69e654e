# 🎨 Guide Gemini CLI UI - Interface Graphique Française

> **Interface web responsive pour Gemini CLI avec configuration française personnalisée**

## 🎯 À Propos

Gemini CLI UI est une interface web moderne qui vous permet d'utiliser Gemini CLI via une interface graphique intuitive. Cette installation est configurée spécialement pour votre environnement français.

## 🚀 Démarrage Rapide

### Méthode 1 : Script PowerShell (Recommandé)
```powershell
# Démarrage avec vérifications automatiques
.\start-gemini-ui.ps1
```

### Méthode 2 : Script Batch Simple
```batch
# Démarrage rapide
.\start-gemini-ui.bat
```

### Méthode 3 : Commandes <PERSON>
```powershell
# Navigation vers le dossier UI
cd ui

# Démarrage des serveurs
npm run dev
```

## 🌐 Accès à l'Interface

Une fois les serveurs démarrés :

- **Interface Utilisateur** : http://localhost:4009
- **API Backend** : http://localhost:4008
- **Arrêt** : Ctrl+C dans le terminal

## 🔐 Première Connexion

### 1. Création du Compte
- Ouvrez http://localhost:4009 dans votre navigateur
- Créez un compte utilisateur (première utilisation)
- Connectez-vous avec vos identifiants

### 2. Configuration des Outils
⚠️ **Important** : Tous les outils Gemini CLI sont désactivés par défaut pour la sécurité.

Pour activer les outils :
1. Cliquez sur l'icône ⚙️ (paramètres) dans la barre latérale
2. Activez sélectivement les outils dont vous avez besoin
3. Cliquez sur "Sauvegarder les paramètres"

### 3. Outils Recommandés à Activer
- **File Operations** : Lecture/écriture de fichiers
- **Terminal** : Accès au terminal intégré
- **Git** : Opérations Git de base
- **Project Management** : Gestion des projets

## 🎮 Fonctionnalités Principales

### 💬 Interface de Chat
- Communication en temps réel avec Gemini CLI
- Historique des conversations
- Support des images (upload et questions)
- Gestion des sessions multiples

### 📁 Explorateur de Fichiers
- Arborescence interactive des projets
- Éditeur de code avec coloration syntaxique
- Édition en direct des fichiers
- Opérations sur les fichiers (créer, renommer, supprimer)

### 🔧 Terminal Intégré
- Accès direct à Gemini CLI
- Exécution de commandes système
- Interface shell complète

### 🌿 Intégration Git
- Visualisation des changements
- Staging et commits
- Gestion des branches
- Historique des commits

### 📱 Design Responsive
- Interface adaptée desktop/mobile
- Navigation tactile optimisée
- Barre de navigation inférieure sur mobile
- Mode PWA (ajout à l'écran d'accueil)

## ⚙️ Configuration Avancée

### Variables d'Environnement
Le fichier `ui/.env` contient :
```bash
PORT=4008                    # Port du serveur backend
VITE_PORT=4009              # Port de l'interface
GEMINI_PATH=npx @google/gemini-cli  # Chemin vers Gemini CLI
JWT_SECRET=cisco-gemini-ui-secret-key-2025  # Clé de sécurité
NODE_ENV=development        # Environnement
LANG=fr                     # Langue française
LOCALE=fr-FR               # Localisation française
```

### Personnalisation des Ports
Pour changer les ports, modifiez le fichier `ui/.env` :
```bash
PORT=8008      # Nouveau port backend
VITE_PORT=8009 # Nouveau port frontend
```

## 🛠️ Dépannage

### Problème : "No Gemini projects found"
**Solution** :
1. Assurez-vous que Gemini CLI est installé
2. Exécutez `npx @google/gemini-cli` dans au moins un projet
3. Vérifiez que le dossier `~/.gemini/projects/` existe

### Problème : Erreurs de compilation node-pty
**Solution** :
```powershell
# Installation sans dépendances optionnelles
cd ui
npm install --omit=optional
```

### Problème : Ports déjà utilisés
**Solution** :
1. Modifiez les ports dans `ui/.env`
2. Ou arrêtez les processus utilisant les ports 4008/4009

### Problème : Gemini CLI non trouvé
**Solution** :
1. Vérifiez l'installation : `npx @google/gemini-cli --version`
2. Modifiez `GEMINI_PATH` dans `ui/.env` si nécessaire

## 🔒 Sécurité

### Bonnes Pratiques
- ✅ Activez uniquement les outils nécessaires
- ✅ Utilisez des mots de passe forts
- ✅ Changez le JWT_SECRET en production
- ✅ Surveillez les logs d'activité

### Mode YOLO
⚠️ **Attention** : Le mode YOLO désactive les confirmations.
- Utilisez avec précaution
- Équivalent au flag `--yolo` de Gemini CLI
- Recommandé uniquement pour les utilisateurs expérimentés

## 📞 Support

### Ressources
- [Documentation Gemini CLI](https://github.com/google-gemini/gemini-cli)
- [Projet Gemini CLI UI](https://github.com/cruzyjapan/Gemini-CLI-UI)
- [Google AI Studio](https://aistudio.google.com/)

### Logs et Débogage
- Logs backend : Console du serveur
- Logs frontend : Console du navigateur (F12)
- Base de données : `ui/server/database/geminicliui_auth.db`

---

**📝 Note** : Cette interface complète parfaitement votre configuration Gemini CLI existante. Vos scripts et configurations restent inchangés.
