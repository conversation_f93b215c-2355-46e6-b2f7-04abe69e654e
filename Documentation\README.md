# 📝 Bloc-Notes Personnel - Gemini CLI

> **Configuration française personnalisée et sécurisée pour Gemini CLI**

## 🎯 À Propos

Ce dépôt est mon **bloc-notes personnel** pour Gemini CLI. Il contient :
- ✅ Mes configurations personnalisées en français
- ✅ Mes scripts et astuces d'utilisation
- ✅ Ma documentation technique personnelle
- ✅ Mes serveurs MCP configurés
- ✅ Mes guides et mémos

**⚠️ Important :** Ce dépôt ne contient **PAS** le code source de Gemini CLI. J'utilise `npx @google/gemini-cli` pour avoir toujours la dernière version.

## 🚀 Utilisation Rapide

### Interface Graphique (Nouveau !)
```powershell
# Lancer l'interface web Gemini CLI UI
.\start-gemini-ui.ps1

# Ou version simple
.\start-gemini-ui.bat
```
**Accès** : http://localhost:4009 (Interface) + http://localhost:4008 (API)

### Méthode Recommandée (NPX)
```bash
# Lancer Gemini CLI avec ma configuration française
npx @google/gemini-cli
```

### Mes Scripts Personnalisés
```powershell
# Script de démarrage avec configuration française
.\start-gemini.ps1

# Script de démarrage avec NPX
.\start-gemini-npx.ps1

# Test de la configuration française
.\test-francais.ps1

# Test des serveurs MCP
.\test-serveurs-mcp.ps1
```

## 📚 Documentation Personnelle

### 🔧 Configuration et Installation
- [`INSTALLATION-COMPLETE.md`](INSTALLATION-COMPLETE.md) - Guide d'installation complet
- [`DOCUMENTATION-TECHNIQUE.md`](DOCUMENTATION-TECHNIQUE.md) - Documentation technique détaillée
- [`SECURITE-API-KEYS.md`](SECURITE-API-KEYS.md) - Sécurité des clés API

### 🎮 Guides d'Utilisation
- [`GUIDE-GEMINI-CLI-UI.md`](GUIDE-GEMINI-CLI-UI.md) - **Interface graphique web (Nouveau !)**
- [`GUIDE-UTILISATION-GEMINI.md`](GUIDE-UTILISATION-GEMINI.md) - Guide d'utilisation complet
- [`GUIDE-CODE-ASSIST.md`](GUIDE-CODE-ASSIST.md) - Guide VS Code avec Gemini
- [`COMPARAISON-METHODES.md`](COMPARAISON-METHODES.md) - Comparaison des méthodes d'utilisation

### 🔌 Serveurs MCP
- [`GUIDE-CONFIGURATION-MCP.md`](GUIDE-CONFIGURATION-MCP.md) - Configuration des serveurs MCP
- [`SERVEURS-MCP-GRATUITS.md`](SERVEURS-MCP-GRATUITS.md) - Liste des serveurs MCP gratuits

### 📋 Historique et Suivi
- [`TACHES-COMPLETEES.md`](TACHES-COMPLETEES.md) - Historique des tâches réalisées
- [`COMMIT-GITHUB-REUSSI.md`](COMMIT-GITHUB-REUSSI.md) - Historique des commits réussis

## ⚙️ Configuration

### Variables d'Environnement
Créez un fichier `.env` (non versionné) avec :
```bash
GEMINI_API_KEY=votre_cle_api_ici
GEMINI_MODEL=gemini-2.5-pro
GEMINI_SYSTEM_MD=true
```

### Fichiers de Configuration
- `.env.example` - Exemple de configuration
- Scripts PowerShell pour différents usages

## 🔐 Sécurité

- ✅ Aucune clé API n'est stockée dans ce dépôt
- ✅ Fichier `.env` protégé par `.gitignore`
- ✅ Utilisation de placeholders dans tous les scripts
- ✅ Documentation complète sur la sécurité des clés API

## 🛠️ Scripts Disponibles

| Script | Description |
|--------|-------------|
| `start-gemini-ui.ps1` | **Interface graphique web (Nouveau !)** |
| `start-gemini-ui.bat` | **Interface graphique - version simple** |
| `start-gemini.ps1` | Démarrage avec configuration française |
| `start-gemini-npx.ps1` | Démarrage avec NPX (recommandé) |
| `test-francais.ps1` | Test de la configuration française |
| `test-serveurs-mcp.ps1` | Test des serveurs MCP |
| `verifier-installation.ps1` | Vérification de l'installation |
| `comparer-installation.ps1` | Comparaison avec la référence |
| `demo-utilisation.ps1` | Démonstration des différentes méthodes |

## 📞 Support

Pour toute question sur Gemini CLI :
- [Documentation officielle](https://github.com/google-gemini/gemini-cli)
- [Google AI Studio](https://aistudio.google.com/)

---

**📝 Note :** Ce dépôt est un bloc-notes personnel. Pour le code source officiel de Gemini CLI, consultez le [dépôt officiel](https://github.com/google-gemini/gemini-cli).
