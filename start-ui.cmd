@echo off
title Gemini CLI UI - Lancement
echo.
echo ========================================
echo   Gemini CLI UI - Interface Francaise
echo ========================================
echo.

echo Verification de Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERREUR: Node.js non trouve
    echo Installez Node.js depuis https://nodejs.org
    pause
    exit /b 1
)
echo OK - Node.js detecte

echo.
echo Navigation vers le dossier ui...
if not exist "ui" (
    echo ERREUR: Dossier ui non trouve
    echo Verifiez que vous etes dans le bon repertoire
    pause
    exit /b 1
)

cd ui
echo OK - Dans le dossier ui

echo.
echo Verification des dependances...
if not exist "node_modules" (
    echo Installation des dependances...
    npm install --ignore-scripts
    if %errorlevel% neq 0 (
        echo ERREUR: Installation echouee
        pause
        exit /b 1
    )
)
echo OK - Dependances presentes

echo.
echo Verification du fichier .env...
if not exist ".env" (
    echo Creation du fichier .env...
    copy ".env.example" ".env" >nul 2>&1
)
echo OK - Configuration presente

echo.
echo ========================================
echo   LANCEMENT DE L'INTERFACE
echo ========================================
echo.
echo Interface disponible sur:
echo   http://localhost:4009
echo.
echo Appuyez sur Ctrl+C pour arreter
echo.

npm run client

echo.
echo Interface arretee.
pause
