@echo off
title Gemini CLI UI - Lancement
color 0A
echo.
echo ========================================
echo   GEMINI CLI UI - INTERFACE FRANCAISE
echo ========================================
echo.

echo [1/6] Verification de Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERREUR: Node.js non trouve
    echo Installez Node.js depuis https://nodejs.org
    echo.
    pause
    exit /b 1
)
for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo OK - Node.js %NODE_VERSION% detecte

echo.
echo [2/6] Verification du repertoire...
echo Repertoire actuel: %CD%
if not exist "ui" (
    echo ERREUR: Dossier ui non trouve dans %CD%
    echo Verifiez que vous etes dans le bon repertoire
    echo.
    pause
    exit /b 1
)
echo OK - Dossier ui trouve

echo.
echo [3/6] Navigation vers le dossier ui...
cd /d "%~dp0ui"
echo OK - Maintenant dans: %CD%

echo.
echo [4/6] Verification des dependances...
if not exist "node_modules" (
    echo Installation des dependances en cours...
    echo Cela peut prendre quelques minutes...
    npm install --ignore-scripts --silent
    if %errorlevel% neq 0 (
        echo ERREUR: Installation des dependances echouee
        echo.
        pause
        exit /b 1
    )
    echo OK - Dependances installees
) else (
    echo OK - Dependances deja presentes
)

echo.
echo [5/6] Verification de la configuration...
if not exist ".env" (
    echo Creation du fichier .env...
    if exist ".env.example" (
        copy ".env.example" ".env" >nul 2>&1
        echo OK - Fichier .env cree
    ) else (
        echo ATTENTION: .env.example non trouve
    )
) else (
    echo OK - Fichier .env present
)

echo.
echo [6/6] Verification des scripts npm...
npm run --silent >nul 2>&1
if %errorlevel% neq 0 (
    echo ERREUR: npm non fonctionnel
    pause
    exit /b 1
)
echo OK - npm fonctionnel

echo.
echo ========================================
echo   LANCEMENT DE L'INTERFACE
echo ========================================
echo.
echo Interface sera disponible sur:
echo   http://localhost:4009
echo.
echo IMPORTANT:
echo - NE FERMEZ PAS cette fenetre
echo - Appuyez sur Ctrl+C pour arreter
echo - Ouvrez http://localhost:4009 dans votre navigateur
echo.
echo Demarrage en cours...
echo.

npm run client

echo.
echo ========================================
echo Interface arretee.
echo Appuyez sur une touche pour fermer...
pause >nul
