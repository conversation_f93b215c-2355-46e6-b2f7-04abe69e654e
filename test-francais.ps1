# Script de test pour vérifier la configuration française de Gemini CLI

$env:GOOGLE_API_KEY = "YOUR_GEMINI_API_KEY_HERE"
$env:GEMINI_MODEL = "gemini-2.5-pro"
$env:GEMINI_SYSTEM_MD = "true"

Write-Host "🧪 Test de la configuration française de Gemini CLI" -ForegroundColor Cyan
Write-Host "=================================================" -ForegroundColor Cyan
Write-Host ""

# Test 1 : Question simple en français
Write-Host "Test 1 : Question simple..." -ForegroundColor Yellow
$question1 = "Bonjour ! Comment allez-vous ? Pouvez-vous me répondre en français ?"
Write-Host "Question : $question1" -ForegroundColor Gray
Write-Host "Réponse :" -ForegroundColor Green
echo $question1 | node bundle/gemini.js --model gemini-2.5-pro

Write-Host ""
Write-Host "----------------------------------------" -ForegroundColor DarkGray

# Test 2 : Question technique
Write-Host "Test 2 : Question technique..." -ForegroundColor Yellow
$question2 = "Expliquez-moi brièvement ce qu'est JavaScript"
Write-Host "Question : $question2" -ForegroundColor Gray
Write-Host "Réponse :" -ForegroundColor Green
echo $question2 | node bundle/gemini.js --model gemini-2.5-pro

Write-Host ""
Write-Host "🏁 Tests terminés" -ForegroundColor Cyan
