# 🔄 Comparaison des Méthodes d'Installation Gemini CLI

## 📊 Deux Méthodes Disponibles

### 🏠 Méthode 1 : Installation Locale (Notre Configuration)
```powershell
# Installation complète avec configuration française
git clone https://github.com/google-gemini/gemini-cli.git
npm install
npm run build
.\start-gemini.ps1
```

**Avantages :**
- ✅ Configuration française permanente (prompt système traduit)
- ✅ Contrôle total de la configuration
- ✅ Pas de téléchargement à chaque utilisation
- ✅ Scripts personnalisés disponibles
- ✅ Fonctionne hors ligne (une fois installé)

**Inconvénients :**
- ⚠️ Nécessite une installation initiale
- ⚠️ Mises à jour manuelles

### 🌐 Méthode 2 : NPX Direct (Votre Préférence)
```powershell
# Utilisation directe depuis GitHub
npx https://github.com/google-gemini/gemini-cli --model gemini-2.5-pro
```

**Avantages :**
- ✅ Toujours la dernière version
- ✅ Pas d'installation locale nécessaire
- ✅ Commande simple et directe
- ✅ Mises à jour automatiques

**Inconvénients :**
- ⚠️ Téléchargement à chaque utilisation
- ⚠️ Pas de configuration française permanente
- ⚠️ Nécessite une connexion internet

## 🎯 Configuration Actuelle (Mise à Jour)

Nous avons maintenant configuré **les deux méthodes** avec **Gemini 2.5 Pro** comme vous le souhaitiez :

### 📁 Scripts Disponibles

1. **Installation locale avec Gemini 2.5 Pro :**
   ```powershell
   .\start-gemini.ps1          # Utilise notre installation locale
   ```

2. **NPX avec Gemini 2.5 Pro :**
   ```powershell
   .\start-gemini-npx.ps1      # Utilise npx directement
   ```

3. **Commande directe NPX :**
   ```powershell
   npx https://github.com/google-gemini/gemini-cli --model gemini-2.5-pro
   ```

## 🧪 Tests de Validation

### Test Installation Locale
```powershell
$env:GEMINI_API_KEY="YOUR_GEMINI_API_KEY_HERE"
echo "Bonjour !" | node bundle/gemini.js --model gemini-2.5-pro
```

### Test NPX
```powershell
$env:GEMINI_API_KEY="YOUR_GEMINI_API_KEY_HERE"
echo "Bonjour !" | npx https://github.com/google-gemini/gemini-cli --model gemini-2.5-pro
```

## 📋 Configuration Mise à Jour

### Variables d'Environnement
```bash
GEMINI_API_KEY=YOUR_GEMINI_API_KEY_HERE
GEMINI_MODEL=gemini-2.5-pro  # ✅ MISE À JOUR
GEMINI_SYSTEM_MD=true
```

### Fichiers Modifiés
- ✅ `.gemini/.env` → Modèle changé pour gemini-2.5-pro
- ✅ `.gemini/settings.json` → Modèle changé pour gemini-2.5-pro
- ✅ `start-gemini.bat` → Modèle changé pour gemini-2.5-pro
- ✅ `start-gemini.ps1` → Modèle changé pour gemini-2.5-pro
- ✅ `test-francais.ps1` → Modèle changé pour gemini-2.5-pro
- ✅ `start-gemini-npx.ps1` → Nouveau script NPX

## 🏆 Recommandation

**Pour votre usage :** Utilisez la **méthode NPX** que vous préférez :
```powershell
npx https://github.com/google-gemini/gemini-cli --model gemini-2.5-pro
```

**Pour une configuration française permanente :** Utilisez notre installation locale :
```powershell
.\start-gemini.ps1
```

## 🎯 Résultat Final

✅ **Les deux méthodes sont maintenant configurées avec Gemini 2.5 Pro**
✅ **Votre préférence NPX est disponible et fonctionnelle**
✅ **Notre installation locale conserve la configuration française**
✅ **Vous avez le choix entre les deux approches**
