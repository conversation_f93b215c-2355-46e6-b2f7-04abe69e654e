# 🔧 Guide de Configuration MCP (Model Context Protocol)

## 🎯 Qu'est-ce que MCP ?

Le **Model Context Protocol (MCP)** permet à Gemini CLI d'accéder à des outils et ressources externes via des serveurs spécialisés. <PERSON>la étend considérablement les capacités de Gemini.

## ✅ État Actuel de Votre Installation

### 🟢 **SUPPORT MCP DISPONIBLE**
- ✅ SDK MCP installé : `@modelcontextprotocol/sdk v1.11.0`
- ✅ Infrastructure MCP complète
- ✅ Configuration MCP supportée dans `settings.json`

### 🟡 **CONTEXT7 NON CONFIGURÉ**
- ❌ Aucun serveur MCP configuré actuellement
- ❌ Context7 MCP Server non installé

## 🚀 Configuration de Context7

### 1. **Qu'est-ce que Context7 ?**
Context7 est un serveur MCP qui fournit :
- 📚 Accès à la documentation de bibliothèques
- 🔍 Recherche contextuelle dans les projets
- 📖 Résolution automatique d'identifiants de bibliothèques
- 🛠️ Outils de développement avancés

### 2. **Installation et Configuration**

#### Option A : Configuration Automatique (RECOMMANDÉE)
```bash
# Installer Context7 MCP Server
npm install -g @context7/mcp-server

# Ou utiliser npx (pas d'installation permanente)
npx -y @context7/mcp-server --help
```

#### Option B : Configuration Manuelle
Copiez le fichier d'exemple :
```bash
cp .gemini/settings-with-context7.json.example .gemini/settings.json
```

### 3. **Configuration dans settings.json**

Ajoutez cette section à votre `.gemini/settings.json` :

```json
{
  "mcpServers": {
    "context7": {
      "command": "npx",
      "args": ["-y", "@context7/mcp-server"],
      "description": "Context7 MCP Server",
      "trust": true,
      "timeout": 10000
    }
  }
}
```

## 🔧 Autres Serveurs MCP Utiles

### 1. **Serveur Filesystem**
```json
"filesystem": {
  "command": "npx",
  "args": ["-y", "@modelcontextprotocol/server-filesystem", "/chemin/autorisé"],
  "description": "Opérations sur fichiers",
  "trust": false
}
```

### 2. **Serveur Web Search**
```json
"web-search": {
  "command": "npx",
  "args": ["-y", "@modelcontextprotocol/server-web-search"],
  "description": "Recherche web",
  "trust": false,
  "env": {
    "SEARCH_API_KEY": "${VOTRE_CLE_RECHERCHE}"
  }
}
```

## 🧪 Test de la Configuration

### 1. **Vérifier la Configuration**
```powershell
# Démarrer Gemini CLI
.\start-gemini.ps1

# Dans le chat Gemini, taper :
# "Quels outils MCP sont disponibles ?"
```

### 2. **Tester Context7**
```powershell
# Dans le chat Gemini :
# "Peux-tu utiliser Context7 pour chercher la documentation de React ?"
```

## 🔐 Sécurité MCP

### ⚠️ **Paramètre `trust`**
- `"trust": true` → Exécute les outils sans confirmation
- `"trust": false` → Demande confirmation avant chaque utilisation

### 🛡️ **Bonnes Pratiques**
1. **Serveurs de confiance** : Seulement pour les outils que vous connaissez
2. **Variables d'environnement** : Pour les clés API sensibles
3. **Timeouts** : Éviter les blocages avec des serveurs lents

## 🚨 Dépannage

### Problème : "MCP Server not found"
```bash
# Vérifier l'installation
npx @context7/mcp-server --version

# Réinstaller si nécessaire
npm install -g @context7/mcp-server
```

### Problème : "Connection timeout"
```json
{
  "timeout": 15000  // Augmenter le timeout
}
```

### Problème : "Permission denied"
```json
{
  "trust": true  // Ou vérifier les permissions
}
```

## 📋 Configuration Complète Recommandée

Voici une configuration complète avec Context7 et autres outils utiles :

```json
{
  "auth": {
    "type": "api_key",
    "api_key": "YOUR_GEMINI_API_KEY_HERE"
  },
  "model": "gemini-2.5-pro",
  "contextFileName": "system.md",
  "mcpServers": {
    "context7": {
      "command": "npx",
      "args": ["-y", "@context7/mcp-server"],
      "description": "Context7 - Documentation et contexte",
      "trust": true,
      "timeout": 10000
    }
  },
  "telemetry": {
    "enabled": false
  },
  "usageStatisticsEnabled": false
}
```

## 🎯 Prochaines Étapes

1. **Remplacez** `YOUR_GEMINI_API_KEY_HERE` par votre vraie clé API
2. **Testez** la configuration avec `.\start-gemini.ps1`
3. **Explorez** les capacités Context7 dans le chat
4. **Ajoutez** d'autres serveurs MCP selon vos besoins
