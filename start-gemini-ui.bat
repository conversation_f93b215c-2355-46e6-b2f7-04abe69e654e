@echo off
echo 🚀 Démarrage de Gemini CLI UI - Interface Française
echo =================================================
echo.
echo Vérification de Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js n'est pas installé ou non accessible
    echo    Veuillez installer Node.js v20 ou supérieur
    pause
    exit /b 1
)
echo ✅ Node.js détecté

echo.
echo Navigation vers le dossier UI...
if not exist "ui" (
    echo ❌ Dossier ui non trouvé
    echo    Assurez-vous d'être dans le bon répertoire
    pause
    exit /b 1
)
cd ui

echo.
echo Vérification des dépendances...
if not exist "node_modules" (
    echo 📦 Installation des dépendances...
    npm install --ignore-scripts
    if errorlevel 1 (
        echo ❌ Erreur lors de l'installation
        pause
        exit /b 1
    )
)

echo.
echo Vérification du fichier .env...
if not exist ".env" (
    echo ⚠️ Copie du fichier .env depuis .env.example...
    copy ".env.example" ".env" >nul
)

echo.
echo 🌐 Démarrage de l'interface...
echo    Interface UI: http://localhost:4009
echo    API Backend: http://localhost:4008
echo.
echo 📝 Instructions:
echo    1. L'interface s'ouvrira dans votre navigateur
echo    2. Créez un compte lors de la première connexion
echo    3. Activez les outils dans les paramètres
echo    4. Utilisez Ctrl+C pour arrêter
echo.

npm run dev
