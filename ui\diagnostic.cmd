@echo off
title DIAGNOSTIC GEMINI CLI UI
color 0E
cls

echo ========================================
echo   DIAGNOSTIC GEMINI CLI UI
echo ========================================
echo.

echo [TEST 1] Verification de Node.js...
node --version
if %errorlevel% neq 0 (
    echo ECHEC: Node.js non trouve
    goto :fin
)
echo SUCCES: Node.js detecte
echo.

echo [TEST 2] Verification de npm...
npm --version
if %errorlevel% neq 0 (
    echo ECHEC: npm non trouve
    goto :fin
)
echo SUCCES: npm detecte
echo.

echo [TEST 3] Verification du repertoire...
echo Repertoire actuel: %CD%
if not exist "package.json" (
    echo ECHEC: package.json non trouve
    echo Vous n'etes pas dans le bon dossier
    goto :fin
)
echo SUCCES: package.json trouve
echo.

echo [TEST 4] Verification des dependances...
if not exist "node_modules" (
    echo ATTENTION: node_modules manquant
    echo Installation en cours...
    npm install --ignore-scripts
    if %errorlevel% neq 0 (
        echo ECHEC: Installation des dependances
        goto :fin
    )
)
echo SUCCES: node_modules present
echo.

echo [TEST 5] Verification des scripts npm...
npm run
echo.

echo [TEST 6] Test du serveur backend...
echo Tentative de demarrage du serveur...
timeout /t 2 >nul
start /b npm run server
timeout /t 5 >nul
echo.

echo [TEST 7] Verification des ports...
netstat -an | find "4008"
netstat -an | find "4009"
echo.

echo ========================================
echo   TOUS LES TESTS TERMINES
echo ========================================
echo.

:fin
echo.
echo Appuyez sur une touche pour continuer...
pause >nul
echo.
echo Voulez-vous lancer l'interface maintenant ? (O/N)
set /p choix=
if /i "%choix%"=="O" (
    echo.
    echo Lancement de l'interface complete...
    echo GARDEZ CETTE FENETRE OUVERTE !
    echo.
    npm run dev
)

echo.
echo FIN DU DIAGNOSTIC
pause
