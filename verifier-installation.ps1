# Script de vérification de l'installation Gemini CLI
# Ce script vérifie que votre installation est identique à celle configurée

Write-Host "🔍 VÉRIFICATION DE L'INSTALLATION GEMINI CLI" -ForegroundColor Cyan
Write-Host "=============================================" -ForegroundColor Cyan
Write-Host ""

# Vérification de la structure des fichiers
Write-Host "📁 Vérification de la structure des fichiers..." -ForegroundColor Yellow

$fichiers_requis = @(
    "bundle/gemini.js",
    ".gemini/system.md",
    ".gemini/settings.json",
    ".gemini/.env",
    "start-gemini.bat",
    "start-gemini.ps1",
    "README-FRANCAIS.md"
)

foreach ($fichier in $fichiers_requis) {
    if (Test-Path $fichier) {
        Write-Host "✅ $fichier" -ForegroundColor Green
    } else {
        Write-Host "❌ $fichier (MANQUANT)" -ForegroundColor Red
    }
}

Write-Host ""

# Vérification de la configuration
Write-Host "⚙️ Vérification de la configuration..." -ForegroundColor Yellow

# Vérifier le contenu du fichier settings.json
if (Test-Path ".gemini/settings.json") {
    $settings = Get-Content ".gemini/settings.json" | ConvertFrom-Json
    Write-Host "✅ Fichier settings.json valide" -ForegroundColor Green
    Write-Host "   - Type d'auth: $($settings.auth.type)" -ForegroundColor Gray
    Write-Host "   - Modèle: $($settings.model)" -ForegroundColor Gray
    Write-Host "   - Fichier contexte: $($settings.contextFileName)" -ForegroundColor Gray
} else {
    Write-Host "❌ Fichier settings.json manquant" -ForegroundColor Red
}

Write-Host ""

# Vérification de la clé API
Write-Host "🔑 Vérification de la clé API..." -ForegroundColor Yellow
$env:GEMINI_API_KEY = "YOUR_GEMINI_API_KEY_HERE"
$env:GEMINI_SYSTEM_MD = "true"

if ($env:GEMINI_API_KEY) {
    Write-Host "✅ Clé API configurée" -ForegroundColor Green
} else {
    Write-Host "❌ Clé API manquante" -ForegroundColor Red
}

Write-Host ""

# Test de fonctionnement
Write-Host "🧪 Test de fonctionnement..." -ForegroundColor Yellow
Write-Host "Question test : 'Bonjour, répondez en français'" -ForegroundColor Gray

try {
    $result = echo "Bonjour, répondez en français" | node bundle/gemini.js --model gemini-1.5-flash 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Test réussi - Gemini répond correctement" -ForegroundColor Green
        Write-Host "Réponse: $($result -join ' ')" -ForegroundColor Gray
    } else {
        Write-Host "❌ Test échoué" -ForegroundColor Red
        Write-Host "Erreur: $result" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Erreur lors du test: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "🎯 COMMANDES POUR LANCER GEMINI CLI:" -ForegroundColor Cyan
Write-Host "=====================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "1. Mode interactif (recommandé):" -ForegroundColor Yellow
Write-Host "   .\start-gemini.ps1" -ForegroundColor White
Write-Host ""
Write-Host "2. Commande directe:" -ForegroundColor Yellow
Write-Host "   node bundle/gemini.js --model gemini-1.5-flash" -ForegroundColor White
Write-Host ""
Write-Host "3. Avec une question directe:" -ForegroundColor Yellow
Write-Host "   echo 'Votre question' | node bundle/gemini.js --model gemini-1.5-flash" -ForegroundColor White
Write-Host ""
Write-Host "4. Mode debug:" -ForegroundColor Yellow
Write-Host "   node bundle/gemini.js --model gemini-1.5-flash --debug" -ForegroundColor White
