<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 64 64" width="64" height="64">
  <defs>
    <linearGradient id="geminiGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#06b6d4;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#0891b2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0e7490;stop-opacity:1" />
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background with gradient -->
  <rect x="0" y="0" width="64" height="64" rx="12" fill="url(#geminiGradient)"/>
  
  <!-- Gemini constellation symbol -->
  <g transform="translate(32,32)" filter="url(#glow)">
    <!-- Top star shape -->
    <path d="M0,-20 L5,0 L-5,0 Z" fill="white" opacity="0.95"/>
    <!-- Bottom star shape -->
    <path d="M0,20 L-5,0 L5,0 Z" fill="white" opacity="0.85"/>
    <!-- Connecting lines -->
    <circle cx="0" cy="-10" r="3" fill="white" opacity="0.9"/>
    <circle cx="0" cy="10" r="3" fill="white" opacity="0.9"/>
    <line x1="0" y1="-10" x2="0" y2="10" stroke="white" stroke-width="2" opacity="0.7"/>
  </g>
</svg>