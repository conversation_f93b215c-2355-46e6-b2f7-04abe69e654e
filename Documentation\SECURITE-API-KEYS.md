# 🔐 Sécurité des Clés API - Instructions Importantes

## ⚠️ AVERTISSEMENT DE SÉCURITÉ

Ce projet contient des scripts et de la documentation qui nécessitent une clé API Gemini. Pour des raisons de sécurité, **AUCUNE clé API réelle n'est incluse dans ce dépôt**.

## 🔑 Configuration de votre Clé API

### 1. Obtenir votre Clé API
- Rendez-vous sur [Google AI Studio](https://aistudio.google.com/app/apikey)
- Créez une nouvelle clé API
- Copiez la clé (format : `AIzaSy...`)

### 2. Configuration Locale

#### Option A : Variables d'environnement (RECOMMANDÉ)
```powershell
# Dans PowerShell
$env:GEMINI_API_KEY="VOTRE_VRAIE_CLE_API_ICI"
$env:GOOGLE_API_KEY="VOTRE_VRAIE_CLE_API_ICI"
```

#### Option B : Fichier .env
```bash
# Copiez .env.example vers .env
cp .env.example .env

# Éditez .env avec vos vraies valeurs
GEMINI_API_KEY=VOTRE_VRAIE_CLE_API_ICI
GOOGLE_API_KEY=VOTRE_VRAIE_CLE_API_ICI
```

#### Option C : Modification directe des scripts (NON RECOMMANDÉ)
Si vous modifiez directement les scripts, **NE JAMAIS** commiter ces modifications avec vos vraies clés API.

## 🛡️ Bonnes Pratiques de Sécurité

### ✅ À FAIRE :
- Utiliser des variables d'environnement
- Garder vos clés API privées
- Utiliser des fichiers .env (ajoutés au .gitignore)
- Régénérer vos clés si elles sont compromises

### ❌ À NE JAMAIS FAIRE :
- Commiter des clés API dans le code
- Partager vos clés API
- Laisser des clés API dans des fichiers publics
- Utiliser des clés API en production sans restrictions

## 🔄 Remplacement des Placeholders

Dans tous les fichiers de ce projet, remplacez :
- `YOUR_GEMINI_API_KEY_HERE` par votre vraie clé API
- Ou configurez les variables d'environnement appropriées

## 📋 Fichiers Concernés

Les fichiers suivants contiennent des placeholders à remplacer :
- `start-gemini.ps1`
- `start-gemini-npx.ps1`
- `start-gemini.bat`
- `test-francais.ps1`
- `demo-utilisation.ps1`
- `comparer-installation.ps1`
- `verifier-installation.ps1`
- Tous les fichiers de documentation (*.md)

## 🆘 En Cas de Compromission

Si votre clé API est compromise :
1. Allez sur [Google AI Studio](https://aistudio.google.com/app/apikey)
2. Révoquezla clé compromise
3. Générez une nouvelle clé
4. Mettez à jour votre configuration locale

## 📞 Support

Pour toute question de sécurité, consultez la [documentation officielle de Google AI](https://ai.google.dev/docs/api_key_best_practices).
