# Script pour pousser la configuration française vers GitHub

Write-Host "🚀 PUSH CONFIGURATION FRANÇAISE VERS GITHUB" -ForegroundColor Cyan
Write-Host "=============================================" -ForegroundColor Cyan
Write-Host ""

# Vérifier l'état actuel
Write-Host "📋 État actuel du dépôt :" -ForegroundColor Yellow
git status --short
Write-Host ""

# Afficher le dernier commit
Write-Host "📝 Dernier commit :" -ForegroundColor Yellow
git log --oneline -1
Write-Host ""

# Afficher les remotes
Write-Host "🔗 Remotes configurés :" -ForegroundColor Yellow
git remote -v
Write-Host ""

Write-Host "⚠️  IMPORTANT :" -ForegroundColor Red
Write-Host "Le remote 'origin' pointe vers le dépôt original de Google." -ForegroundColor Yellow
Write-Host "Pour pousser votre configuration, vous devez :" -ForegroundColor Yellow
Write-Host ""
Write-Host "1. Créer un FORK du dépôt sur GitHub :" -ForegroundColor Cyan
Write-Host "   - Aller sur https://github.com/google-gemini/gemini-cli" -ForegroundColor White
Write-Host "   - Cliquer sur 'Fork' en haut à droite" -ForegroundColor White
Write-Host "   - Créer le fork dans votre compte" -ForegroundColor White
Write-Host ""
Write-Host "2. Changer le remote vers votre fork :" -ForegroundColor Cyan
Write-Host "   git remote set-url origin https://github.com/VOTRE-USERNAME/gemini-cli.git" -ForegroundColor White
Write-Host ""
Write-Host "3. Pousser la branche :" -ForegroundColor Cyan
Write-Host "   git push -u origin configuration-francaise" -ForegroundColor White
Write-Host ""

# Proposer de faire le push automatiquement si l'utilisateur a configuré son remote
Write-Host "🤔 Voulez-vous configurer le remote maintenant ?" -ForegroundColor Yellow
Write-Host "Entrez votre nom d'utilisateur GitHub (ou appuyez sur Entrée pour ignorer) :" -ForegroundColor Yellow

$username = Read-Host

if ($username -and $username.Trim() -ne "") {
    Write-Host ""
    Write-Host "🔧 Configuration du remote vers votre fork..." -ForegroundColor Green
    
    $newRemoteUrl = "https://github.com/$username/gemini-cli.git"
    
    try {
        # Changer l'URL du remote origin
        git remote set-url origin $newRemoteUrl
        Write-Host "✅ Remote configuré vers : $newRemoteUrl" -ForegroundColor Green
        
        Write-Host ""
        Write-Host "🚀 Push de la branche configuration-francaise..." -ForegroundColor Green
        
        # Pousser la branche
        git push -u origin configuration-francaise
        
        Write-Host ""
        Write-Host "🎉 SUCCÈS !" -ForegroundColor Green -BackgroundColor DarkGreen
        Write-Host "Votre configuration française a été poussée vers GitHub !" -ForegroundColor Green
        Write-Host ""
        Write-Host "🔗 Votre dépôt : https://github.com/$username/gemini-cli" -ForegroundColor Cyan
        Write-Host "🌿 Branche : configuration-francaise" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "📋 Prochaines étapes :" -ForegroundColor Yellow
        Write-Host "1. Aller sur votre dépôt GitHub" -ForegroundColor White
        Write-Host "2. Créer une Pull Request si vous voulez contribuer au projet original" -ForegroundColor White
        Write-Host "3. Ou garder votre fork comme version personnalisée" -ForegroundColor White
        
    } catch {
        Write-Host "❌ Erreur lors du push : $($_.Exception.Message)" -ForegroundColor Red
        Write-Host ""
        Write-Host "🔧 Vérifiez que :" -ForegroundColor Yellow
        Write-Host "1. Vous avez créé le fork sur GitHub" -ForegroundColor White
        Write-Host "2. Votre nom d'utilisateur est correct" -ForegroundColor White
        Write-Host "3. Vous êtes authentifié avec Git (git config user.name et user.email)" -ForegroundColor White
    }
} else {
    Write-Host ""
    Write-Host "ℹ️  Configuration manuelle requise." -ForegroundColor Blue
    Write-Host "Suivez les étapes ci-dessus pour pousser vers votre fork." -ForegroundColor Blue
}

Write-Host ""
Write-Host "📁 Fichiers dans votre configuration française :" -ForegroundColor Yellow
git ls-tree --name-only HEAD | Where-Object { $_ -match "\.(md|ps1|bat)$" -and $_ -notmatch "node_modules" } | Sort-Object
