# Script de comparaison d'installation Gemini CLI
# Compare votre installation avec la configuration de référence

Write-Host "🔄 COMPARAISON D'INSTALLATION GEMINI CLI" -ForegroundColor Cyan
Write-Host "=========================================" -ForegroundColor Cyan
Write-Host ""

# Informations de référence
$reference = @{
    "Version Node.js" = ">=18.0.0"
    "Dépôt GitHub" = "https://github.com/google-gemini/gemini-cli.git"
    "Modèle par défaut" = "gemini-1.5-flash"
    "Prompt français" = "Activé (.gemini/system.md)"
    "Clé API" = "YOUR_GEMINI_API_KEY_HERE"
}

Write-Host "📋 Configuration de référence:" -ForegroundColor Yellow
foreach ($item in $reference.GetEnumerator()) {
    Write-Host "   $($item.Key): $($item.Value)" -ForegroundColor Gray
}

Write-Host ""

# Vérification de la version Node.js
Write-Host "🔍 Vérification de l'environnement..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js version: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js non trouvé" -ForegroundColor Red
}

# Vérification du dépôt Git
Write-Host ""
Write-Host "📦 Vérification du dépôt..." -ForegroundColor Yellow
if (Test-Path ".git") {
    try {
        $remoteUrl = git remote get-url origin 2>$null
        if ($remoteUrl -eq "https://github.com/google-gemini/gemini-cli.git") {
            Write-Host "✅ Dépôt GitHub correct" -ForegroundColor Green
        } else {
            Write-Host "⚠️  Dépôt différent: $remoteUrl" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "❌ Impossible de vérifier le dépôt Git" -ForegroundColor Red
    }
} else {
    Write-Host "❌ Pas de dépôt Git trouvé" -ForegroundColor Red
}

# Vérification des hash de fichiers critiques
Write-Host ""
Write-Host "🔐 Vérification de l'intégrité des fichiers..." -ForegroundColor Yellow

$fichiers_critiques = @(
    "package.json",
    "bundle/gemini.js"
)

foreach ($fichier in $fichiers_critiques) {
    if (Test-Path $fichier) {
        $hash = Get-FileHash $fichier -Algorithm SHA256
        Write-Host "✅ $fichier" -ForegroundColor Green
        Write-Host "   SHA256: $($hash.Hash.Substring(0,16))..." -ForegroundColor Gray
    } else {
        Write-Host "❌ $fichier manquant" -ForegroundColor Red
    }
}

Write-Host ""

# Test de fonctionnement comparatif
Write-Host "🧪 Test de fonctionnement comparatif..." -ForegroundColor Yellow

# Configuration des variables d'environnement
$env:GEMINI_API_KEY = "YOUR_GEMINI_API_KEY_HERE"
$env:GEMINI_SYSTEM_MD = "true"

$questions_test = @(
    "Bonjour",
    "Quelle est la capitale de la France ?",
    "Écrivez 'Hello World' en JavaScript"
)

foreach ($question in $questions_test) {
    Write-Host ""
    Write-Host "Test: '$question'" -ForegroundColor Cyan
    try {
        $result = echo $question | node bundle/gemini.js --model gemini-1.5-flash 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Réponse reçue en français" -ForegroundColor Green
            # Afficher les premiers mots de la réponse
            $preview = ($result -join ' ').Substring(0, [Math]::Min(100, ($result -join ' ').Length))
            Write-Host "   Aperçu: $preview..." -ForegroundColor Gray
        } else {
            Write-Host "❌ Erreur dans la réponse" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ Erreur: $($_.Exception.Message)" -ForegroundColor Red
    }
    Start-Sleep -Seconds 2
}

Write-Host ""
Write-Host "✅ COMPARAISON TERMINÉE" -ForegroundColor Green
Write-Host ""
Write-Host "📝 RÉSUMÉ DES COMMANDES PRINCIPALES:" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "• Vérifier l'installation:" -ForegroundColor Yellow
Write-Host "  .\verifier-installation.ps1" -ForegroundColor White
Write-Host ""
Write-Host "• Lancer Gemini CLI (mode interactif):" -ForegroundColor Yellow
Write-Host "  .\start-gemini.ps1" -ForegroundColor White
Write-Host ""
Write-Host "• Lancer Gemini CLI (commande directe):" -ForegroundColor Yellow
Write-Host "  node bundle/gemini.js --model gemini-1.5-flash" -ForegroundColor White
Write-Host ""
Write-Host "• Test rapide:" -ForegroundColor Yellow
Write-Host "  echo 'Bonjour' | node bundle/gemini.js --model gemini-1.5-flash" -ForegroundColor White
