# Documentation Technique - Gemini CLI Français

## 🔧 Modifications Apportées

### 1. Prompt Système Personnalisé

**Fichier** : `.gemini/system.md`

Le prompt système original a été entièrement traduit et adapté en français avec les directives suivantes :

- **Directive impérative** : Toutes les réponses doivent être en français
- **Traduction complète** : Tous les mandats, workflows et guidelines traduits
- **Adaptation culturelle** : Terminologie technique française appropriée
- **Conservation de la structure** : Maintien de la logique et des fonctionnalités originales

### 2. Configuration d'Activation

**Variable d'environnement** : `GEMINI_SYSTEM_MD=true`

Cette variable active l'utilisation du fichier `system.md` personnalisé au lieu du prompt système par défaut.

**Localisation dans le code** :
- Fichier : `packages/core/src/core/prompts.ts`
- Lignes : 22-36
- Fonction : `getCoreSystemPrompt()`

### 3. Configuration d'Authentification

**Fichiers de configuration** :
- `.gemini/settings.json` : Configuration JSON avec clé API
- `.gemini/.env` : Variables d'environnement
- Scripts de démarrage : Variables définies dans les scripts

### 4. Scripts de Démarrage

**Scripts créés** :
- `start-gemini.bat` : Script Batch pour Windows
- `start-gemini.ps1` : Script PowerShell
- `test-francais.ps1` : Script de test

## 📁 Structure des Fichiers

```
.gemini/
├── system.md          # Prompt système français
├── settings.json      # Configuration d'authentification
├── .env              # Variables d'environnement
└── config.yaml       # Configuration existante

Scripts/
├── start-gemini.bat   # Démarrage Batch
├── start-gemini.ps1   # Démarrage PowerShell
└── test-francais.ps1  # Tests de validation
```

## 🔍 Mécanisme de Fonctionnement

### 1. Chargement du Prompt Système

```typescript
// packages/core/src/core/prompts.ts
export function getCoreSystemPrompt(userMemory?: string): string {
  let systemMdEnabled = false;
  let systemMdPath = path.join(GEMINI_CONFIG_DIR, 'system.md');
  const systemMdVar = process.env.GEMINI_SYSTEM_MD?.toLowerCase();
  
  if (systemMdVar && !['0', 'false'].includes(systemMdVar)) {
    systemMdEnabled = true;
    // Charge le fichier system.md personnalisé
    if (fs.existsSync(systemMdPath)) {
      return fs.readFileSync(systemMdPath, 'utf8');
    }
  }
  
  // Sinon, utilise le prompt par défaut
  return defaultPrompt;
}
```

### 2. Configuration des Variables

Les variables d'environnement sont chargées dans cet ordre de priorité :
1. Variables définies dans les scripts
2. Fichier `.gemini/.env`
3. Variables système globales

### 3. Authentification

L'authentification utilise la clé API Google AI Studio configurée dans :
- `GOOGLE_API_KEY` ou `GEMINI_API_KEY`
- Fichier `settings.json`

## 🎯 Points Clés de la Personnalisation

### Directives Françaises Implémentées

1. **Langue obligatoire** : "TOUTES LES RÉPONSES DOIVENT ÊTRE EN FRANÇAIS"
2. **Terminologie technique** : Adaptation des termes techniques en français
3. **Style professionnel** : Maintien du ton professionnel et concis
4. **Conservation des fonctionnalités** : Toutes les capacités techniques préservées

### Fonctionnalités Préservées

- ✅ Outils de développement (edit, read-file, shell, etc.)
- ✅ Gestion de projets et conventions
- ✅ Tests et vérification de code
- ✅ Workflows de développement
- ✅ Sécurité et bonnes pratiques

## 🔄 Processus de Mise à Jour

Pour modifier le prompt système français :

1. Éditer `.gemini/system.md`
2. Redémarrer Gemini CLI
3. Tester avec `test-francais.ps1`

Pour changer la clé API :

1. Modifier `.gemini/.env`
2. Modifier `.gemini/settings.json`
3. Mettre à jour les scripts de démarrage

## 🧪 Tests et Validation

### Script de Test Automatisé

Le fichier `test-francais.ps1` effectue :
- Test de réponse simple en français
- Test de question technique
- Validation de la configuration

### Tests Manuels Recommandés

```powershell
# Test 1 : Salutation
echo "Bonjour" | node bundle/gemini.js

# Test 2 : Question technique
echo "Qu'est-ce que Node.js ?" | node bundle/gemini.js

# Test 3 : Demande de code
echo "Écrivez une fonction JavaScript qui additionne deux nombres" | node bundle/gemini.js
```

## 📊 Métriques de Réussite

- ✅ **100% des réponses en français** : Validation linguistique
- ✅ **Préservation des fonctionnalités** : Tous les outils fonctionnels
- ✅ **Configuration stable** : Paramètres persistants
- ✅ **Documentation complète** : Guides d'utilisation et technique

---

Cette configuration garantit une expérience Gemini CLI entièrement française tout en conservant toutes les capacités techniques avancées de l'outil original.
