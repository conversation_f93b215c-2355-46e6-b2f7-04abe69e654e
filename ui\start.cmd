@echo off
title Gemini CLI UI
color 0A
echo.
echo ========================================
echo   GEMINI CLI UI - INTERFACE FRANCAISE
echo ========================================
echo.

echo Verification de Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERREUR: Node.js non trouve
    echo Installez Node.js depuis https://nodejs.org
    pause
    exit /b 1
)
echo OK - Node.js detecte

echo.
echo Verification des dependances...
if not exist "node_modules" (
    echo Installation des dependances...
    npm install --ignore-scripts
    if %errorlevel% neq 0 (
        echo ERREUR: Installation echouee
        pause
        exit /b 1
    )
)
echo OK - Dependances presentes

echo.
echo Verification de la configuration...
if not exist ".env" (
    if exist ".env.example" (
        copy ".env.example" ".env" >nul
        echo OK - Fichier .env cree
    )
)

echo.
echo ========================================
echo   LANCEMENT DE L'INTERFACE
echo ========================================
echo.
echo Interface disponible sur: http://localhost:4009
echo.
echo IMPORTANT: Gardez cette fenetre ouverte !
echo Appuyez sur Ctrl+C pour arreter
echo.

npm run client

echo.
echo Interface arretee.
pause
