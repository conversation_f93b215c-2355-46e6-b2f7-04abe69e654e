@echo off
REM Script de démarrage pour Gemini CLI en français
REM Configuration des variables d'environnement

set GOOGLE_API_KEY=YOUR_GEMINI_API_KEY_HERE
set GEMINI_MODEL=gemini-2.5-pro
set GEMINI_SYSTEM_MD=true

echo ========================================
echo    GEMINI CLI - VERSION FRANÇAISE
echo ========================================
echo.
echo Configuration:
echo - Modèle: %GEMINI_MODEL%
echo - Prompt système français: Activé
echo - Clé API: Configurée
echo.

REM Démarrage de Gemini CLI
node bundle/gemini.js %*

pause
