# Script de lancement Gemini CLI UI - Configuration Française
# Auteur: Cisco-FlexoDiv
# Version: 1.0

Write-Host "🚀 Démarrage de Gemini CLI UI - Interface Française" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Cyan

# Vérification des prérequis
Write-Host "🔍 Vérification des prérequis..." -ForegroundColor Yellow

# Vérifier Node.js
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js détecté: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js n'est pas installé ou non accessible" -ForegroundColor Red
    Write-Host "   Veuillez installer Node.js v20 ou supérieur" -ForegroundColor Red
    exit 1
}

# Vérifier npm
try {
    $npmVersion = npm --version
    Write-Host "✅ npm détecté: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ npm n'est pas installé ou non accessible" -ForegroundColor Red
    exit 1
}

# Vérifier Gemini CLI
Write-Host "🔍 Vérification de Gemini CLI..." -ForegroundColor Yellow
try {
    $geminiCheck = npx @google/gemini-cli --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Gemini CLI accessible via npx" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Gemini CLI sera installé automatiquement via npx" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️  Gemini CLI sera installé automatiquement via npx" -ForegroundColor Yellow
}

# Naviguer vers le dossier UI
Write-Host "📁 Navigation vers le dossier UI..." -ForegroundColor Yellow
Set-Location ui

# Vérifier l'installation des dépendances
if (-not (Test-Path "node_modules")) {
    Write-Host "📦 Installation des dépendances..." -ForegroundColor Yellow
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Erreur lors de l'installation des dépendances" -ForegroundColor Red
        exit 1
    }
}

# Vérifier le fichier .env
if (-not (Test-Path ".env")) {
    Write-Host "⚠️  Fichier .env manquant, copie depuis .env.example..." -ForegroundColor Yellow
    Copy-Item ".env.example" ".env"
}

Write-Host "🌐 Démarrage des serveurs..." -ForegroundColor Green
Write-Host "   - Backend API: http://localhost:4008" -ForegroundColor Cyan
Write-Host "   - Interface UI: http://localhost:4009" -ForegroundColor Cyan
Write-Host "" -ForegroundColor White
Write-Host "📝 Instructions:" -ForegroundColor Yellow
Write-Host "   1. L'interface s'ouvrira automatiquement dans votre navigateur" -ForegroundColor White
Write-Host "   2. Créez un compte utilisateur lors de la première connexion" -ForegroundColor White
Write-Host "   3. Activez les outils nécessaires dans les paramètres" -ForegroundColor White
Write-Host "   4. Utilisez Ctrl+C pour arrêter les serveurs" -ForegroundColor White
Write-Host "" -ForegroundColor White

# Démarrer l'application
npm run dev
