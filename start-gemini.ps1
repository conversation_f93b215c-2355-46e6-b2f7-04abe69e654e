# Script de démarrage pour Gemini CLI en français
# Configuration des variables d'environnement

$env:GOOGLE_API_KEY = "YOUR_GEMINI_API_KEY_HERE"
$env:GEMINI_MODEL = "gemini-2.5-pro"
$env:GEMINI_SYSTEM_MD = "true"

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    GEMINI CLI - VERSION FRANÇAISE" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "Configuration:" -ForegroundColor Green
Write-Host "- Modèle: $($env:GEMINI_MODEL)" -ForegroundColor Yellow
Write-Host "- Prompt système français: Activé" -ForegroundColor Yellow
Write-Host "- Clé API: Configurée" -ForegroundColor Yellow
Write-Host ""

# Démarrage de Gemini CLI
node bundle/gemini.js $args
