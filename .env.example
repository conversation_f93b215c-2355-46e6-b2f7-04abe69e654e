# Configuration Gemini CLI - Variables d'environnement
# Copiez ce fichier vers .env et remplissez avec vos vraies valeurs

# Clé API Gemini (obligatoire)
# Obtenez votre clé sur : https://aistudio.google.com/app/apikey
GEMINI_API_KEY=YOUR_GEMINI_API_KEY_HERE
GOOGLE_API_KEY=YOUR_GEMINI_API_KEY_HERE

# Modèle Gemini à utiliser
GEMINI_MODEL=gemini-2.5-pro

# Activation du prompt système français
GEMINI_SYSTEM_MD=true

# Configuration optionnelle pour Vertex AI
# GOOGLE_CLOUD_PROJECT=your-project-id
# GOOGLE_GENAI_USE_VERTEXAI=true
