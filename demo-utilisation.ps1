# Démonstration des différentes façons d'utiliser Gemini

$env:GEMINI_API_KEY = "YOUR_GEMINI_API_KEY_HERE"

Write-Host "🎯 DÉMONSTRATION GEMINI - DIFFÉRENTES MÉTHODES" -ForegroundColor Cyan
Write-Host "=============================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "📋 Vous avez 3 options principales :" -ForegroundColor Yellow
Write-Host "1. NPX en ligne de commande (RECOMMANDÉ)" -ForegroundColor Green
Write-Host "2. Installation locale avec configuration française" -ForegroundColor Green  
Write-Host "3. VS Code avec Gemini Code Assist (complément)" -ForegroundColor Green
Write-Host ""

# Démonstration NPX
Write-Host "🚀 DÉMONSTRATION 1 : NPX (Votre méthode préférée)" -ForegroundColor Cyan
Write-Host "=================================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "Commande utilisée :" -ForegroundColor Yellow
Write-Host "npx https://github.com/google-gemini/gemini-cli --model gemini-2.5-pro" -ForegroundColor White
Write-Host ""
Write-Host "Test avec une question simple :" -ForegroundColor Yellow

try {
    $result1 = echo "Bonjour ! Quel est votre modèle ?" | npx https://github.com/google-gemini/gemini-cli --model gemini-2.5-pro 2>&1
    Write-Host "✅ Réponse NPX :" -ForegroundColor Green
    Write-Host $result1 -ForegroundColor Gray
} catch {
    Write-Host "❌ Erreur NPX : $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "----------------------------------------" -ForegroundColor DarkGray
Write-Host ""

# Démonstration Installation Locale
Write-Host "🏠 DÉMONSTRATION 2 : Installation Locale (Configuration française)" -ForegroundColor Cyan
Write-Host "=================================================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "Commande utilisée :" -ForegroundColor Yellow
Write-Host "node bundle/gemini.js --model gemini-2.5-pro" -ForegroundColor White
Write-Host ""
Write-Host "Test avec le prompt français :" -ForegroundColor Yellow

try {
    $env:GEMINI_SYSTEM_MD = "true"
    $result2 = echo "Bonjour ! Répondez en français s'il vous plaît." | node bundle/gemini.js --model gemini-2.5-pro 2>&1
    Write-Host "✅ Réponse Locale (avec prompt français) :" -ForegroundColor Green
    Write-Host $result2 -ForegroundColor Gray
} catch {
    Write-Host "❌ Erreur Installation Locale : $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "----------------------------------------" -ForegroundColor DarkGray
Write-Host ""

# Information sur Code Assist
Write-Host "💻 INFORMATION : VS Code Gemini Code Assist" -ForegroundColor Cyan
Write-Host "===========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "Code Assist est différent - c'est une extension VS Code pour :" -ForegroundColor Yellow
Write-Host "✅ Autocomplétion de code en temps réel" -ForegroundColor Green
Write-Host "✅ Suggestions pendant que vous tapez" -ForegroundColor Green
Write-Host "✅ Correction d'erreurs automatique" -ForegroundColor Green
Write-Host "✅ Refactoring assisté par IA" -ForegroundColor Green
Write-Host ""
Write-Host "Installation :" -ForegroundColor Yellow
Write-Host "1. Ouvrir VS Code" -ForegroundColor White
Write-Host "2. Extensions (Ctrl+Shift+X)" -ForegroundColor White
Write-Host "3. Chercher 'Gemini Code Assist' ou 'Google Cloud Code'" -ForegroundColor White
Write-Host "4. Installer l'extension officielle Google" -ForegroundColor White
Write-Host "5. Configurer avec votre clé API" -ForegroundColor White
Write-Host ""

Write-Host "🎯 RECOMMANDATION FINALE" -ForegroundColor Cyan
Write-Host "========================" -ForegroundColor Cyan
Write-Host ""
Write-Host "✅ CONTINUEZ AVEC NPX" -ForegroundColor Green -BackgroundColor DarkGreen
Write-Host "   C'est votre méthode actuelle qui fonctionne parfaitement !" -ForegroundColor Green
Write-Host ""
Write-Host "📝 Commandes principales à retenir :" -ForegroundColor Yellow
Write-Host ""
Write-Host "1. Question rapide :" -ForegroundColor Cyan
Write-Host "   echo 'Votre question' | npx https://github.com/google-gemini/gemini-cli --model gemini-2.5-pro" -ForegroundColor White
Write-Host ""
Write-Host "2. Mode interactif :" -ForegroundColor Cyan
Write-Host "   npx https://github.com/google-gemini/gemini-cli --model gemini-2.5-pro" -ForegroundColor White
Write-Host ""
Write-Host "3. Avec notre script :" -ForegroundColor Cyan
Write-Host "   .\start-gemini-npx.ps1" -ForegroundColor White
Write-Host ""
Write-Host "🔧 Optionnel : Ajoutez Code Assist dans VS Code pour l'autocompletion" -ForegroundColor Yellow
Write-Host "📖 Guides disponibles : GUIDE-UTILISATION-GEMINI.md et GUIDE-CODE-ASSIST.md" -ForegroundColor Gray
Write-Host ""
Write-Host "🏆 VOUS ÊTES PRÊT À UTILISER GEMINI !" -ForegroundColor Green -BackgroundColor DarkGreen
